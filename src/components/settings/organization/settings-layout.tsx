"use client";

import { NavButton } from "@/components/ui/nav-button";
import {
  PageDescription,
  PageTitle,
  PageWrapper,
} from "@/components/ui/page-structure";

interface Props {
  children: React.ReactNode;
  slug: string;
}

export function SettingsLayout({ children, slug }: Props) {
  return (
    <PageWrapper>
      <PageTitle>Settings</PageTitle>
      <PageDescription>
        Manage your organization details, team, and billing.
      </PageDescription>

      <main className="mt-8">
        <div className="overflow-x-scroll border-b border-gray-200 pb-4 sm:overflow-x-auto">
          <div className="flex items-center space-x-2">
            <NavButton href={`/${slug}/settings`}>General</NavButton>
            <NavButton href={`/${slug}/settings/members`}>
              Team members
            </NavButton>
            <NavButton href={`/${slug}/settings/groups`}>Groups</NavButton>
            <NavButton href={`/${slug}/settings/assignments`}>
              Location assignments
            </NavButton>
          </div>
        </div>

        <div className="mt-6">{children}</div>
      </main>
    </PageWrapper>
  );
}
