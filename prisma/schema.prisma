generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["typedSql", "driverAdapters", "relationJoins"]
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model User {
  id                    String                 @id @default(cuid(2))
  name                  String
  firstName             String
  lastName              String
  email                 String                 @unique
  emailVerified         Boolean
  image                 String?
  role                  String?
  banned                Boolean?
  banReason             String?
  banExpires            DateTime?
  stripeCustomerId      String?
  deletedAt             DateTime?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  accounts              Account[]
  invitations           Invitation[]
  members               Member[]
  passkeys              Passkey[]
  sessions              Session[]
  approvalRequests      ProjectApproval[]      @relation("ProjectApprovalRequester")
  approvalReviews       ProjectApproval[]      @relation("ProjectApprovalApprover")
  approvalNotifications ApprovalNotification[]
  projects              Project[]

  @@index([deletedAt])
  @@map("user")
}

model Subscription {
  id                   String    @id @default(cuid(2))
  plan                 String
  referenceId          String
  stripeCustomerId     String?
  stripeSubscriptionId String?
  status               String
  periodStart          DateTime?
  periodEnd            DateTime?
  cancelAtPeriodEnd    Boolean?
  seats                Int?
  trialStart           DateTime?
  trialEnd             DateTime?
  deletedAt            DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt

  @@index([deletedAt])
  @@map("subscriptions")
}

model Session {
  id                   String    @id @default(cuid(2))
  expiresAt            DateTime
  token                String    @unique
  ipAddress            String?
  userAgent            String?
  userId               String
  impersonatedBy       String?
  deletedAt            DateTime?
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  activeOrganizationId String?

  @@index([userId], map: "session_userId_fkey")
  @@index([deletedAt])
  @@map("session")
}

model Account {
  id                    String    @id @default(cuid(2))
  accountId             String
  providerId            String
  userId                String
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  deletedAt             DateTime?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "account_userId_fkey")
  @@index([deletedAt])
  @@map("account")
}

model Verification {
  id         String    @id @default(cuid(2))
  identifier String
  value      String
  expiresAt  DateTime
  deletedAt  DateTime?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@index([deletedAt])
  @@map("verification")
}

model Passkey {
  id           String    @id @default(cuid(2))
  name         String?
  publicKey    String
  userId       String
  credentialID String
  counter      Int
  deviceType   String
  backedUp     Boolean
  transports   String?
  aaguid       String?
  deletedAt    DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "passkey_userId_fkey")
  @@index([deletedAt])
  @@map("passkey")
}

model Organization {
  id                  String               @id @default(cuid(2))
  name                String
  slug                String?              @unique
  logo                String?
  deletedAt           DateTime?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  metadata            Json?
  invitations         Invitation[]
  members             Member[]
  projects            Project[]
  locations           Location[]
  locationAssignments LocationAssignment[]
  groups              Group[]

  @@index([deletedAt])
  @@map("organizations")
}

model Member {
  id                  String               @id @default(cuid(2))
  organizationId      String
  userId              String
  role                String
  deletedAt           DateTime?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  organization        Organization         @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user                User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  locationAssignments LocationAssignment[]
  groupMembers        GroupMember[]

  @@unique([userId, organizationId], name: "member_userId_organizationId_key")
  @@index([organizationId], map: "members_organizationId_fkey")
  @@index([deletedAt])
  @@map("members")
}

model Invitation {
  id             String       @id @default(cuid(2))
  organizationId String
  email          String
  role           String?
  status         String
  expiresAt      DateTime
  inviterId      String
  deletedAt      DateTime?
  createdAt      DateTime     @default(now())
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([email, organizationId])
  @@index([inviterId], map: "invitations_inviterId_fkey")
  @@index([organizationId], map: "invitations_organizationId_fkey")
  @@index([deletedAt])
  @@map("invitations")
}

model Project {
  id             String            @id @default(cuid(2))
  name           String
  status         String            @default("editing")
  slideDuration  Int?              @default(10)
  startDate      DateTime?
  endDate        DateTime?
  deletedAt      DateTime?
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  creatorId      String?
  creator        User?             @relation(fields: [creatorId], references: [id], onDelete: SetNull)
  organizationId String
  organization   Organization?     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  slides         Slide[]
  locations      Location[]        @relation("ProjectLocations")
  sublocations   SubLocation[]     @relation("ProjectSublocations")
  approvals      ProjectApproval[]

  @@index([creatorId])
  @@index([organizationId])
  @@index([deletedAt])
  @@map("projects")
}

model Slide {
  id        String    @id @default(cuid(2))
  projectId String
  imageUrl  String
  order     String // Fractional indexing string
  duration  Int?
  deletedAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  objectKey String
  project   Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@index([projectId], map: "slides_projectId_fkey")
  @@index([deletedAt])
  @@map("slides")
}

model Location {
  id                  String               @id @default(cuid(2))
  shortId             String?
  name                String
  address             String
  city                String
  state               String
  country             String
  postalCode          String
  sublocations        SubLocation[]
  deletedAt           DateTime?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  organizationId      String
  organization        Organization?        @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  projects            Project[]            @relation("ProjectLocations")
  locationAssignments LocationAssignment[]

  @@unique([organizationId, shortId])
  @@index([shortId])
  @@index([organizationId], map: "locations_organizationId_fkey")
  @@index([deletedAt])
  @@map("locations")
}

// Location assignments are used to assign members to locations
model LocationAssignment {
  id             String    @id @default(cuid(2))
  organizationId String
  memberId       String
  locationId     String
  deletedAt      DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  member       Member       @relation(fields: [memberId], references: [id], onDelete: Cascade)
  location     Location     @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@unique([memberId, locationId])
  @@index([organizationId])
  @@index([memberId])
  @@index([locationId])
  @@index([deletedAt])
  @@map("location_assignments")
}

model SubLocation {
  id         String    @id @default(cuid(2))
  shortId    String?
  name       String
  locationId String
  location   Location  @relation(fields: [locationId], references: [id], onDelete: Cascade)
  projects   Project[] @relation("ProjectSublocations")
  deletedAt  DateTime?
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt

  @@unique([locationId, shortId])
  @@index([shortId])
  @@index([locationId], map: "sublocations_locationId_fkey")
  @@index([deletedAt])
  @@map("sublocations")
}

model ProjectApproval {
  id             String    @id @default(cuid(2))
  projectId      String
  requesterId    String
  approverId     String?
  status         String    @default("pending") // pending, approved, rejected
  submittedAt    DateTime  @default(now())
  reviewedAt     DateTime?
  comments       String?
  reviewComments String?
  deletedAt      DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  project       Project                @relation(fields: [projectId], references: [id], onDelete: Cascade)
  requester     User                   @relation("ProjectApprovalRequester", fields: [requesterId], references: [id])
  approver      User?                  @relation("ProjectApprovalApprover", fields: [approverId], references: [id])
  notifications ApprovalNotification[]

  @@index([projectId])
  @@index([requesterId])
  @@index([approverId])
  @@index([deletedAt])
  @@map("project_approvals")
}

model ApprovalNotification {
  id         String    @id @default(cuid(2))
  approvalId String
  userId     String
  type       String // approval_requested, approved, rejected
  read       Boolean   @default(false)
  deletedAt  DateTime?
  createdAt  DateTime  @default(now())

  approval ProjectApproval @relation(fields: [approvalId], references: [id], onDelete: Cascade)
  user     User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([approvalId])
  @@index([deletedAt])
  @@map("approval_notifications")
}

model Group {
  id             String        @id @default(cuid(2))
  name           String
  organizationId String
  orgnaization   Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  deletedAt      DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  groupMembers   GroupMember[]

  @@index([deletedAt])
  @@index([organizationId])
  @@map("groups")
}

model GroupMember {
  id        String    @id @default(cuid(2))
  groupId   String
  memberId  String
  deletedAt DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  group  Group  @relation(fields: [groupId], references: [id], onDelete: Cascade)
  member Member @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@index([groupId])
  @@index([memberId])
  @@index([deletedAt])
  @@map("group_members")
}
